const {
  processLogs,
  halmosLogsToFunctions,
  Fuzzer,
} = require("@recon-fuzz/log-parser");

const testLogs = `Running 21 tests for test/HalmosDirect.t.sol:HalmosDirect
Counterexample: 
    p_a_address_2ca5aa8_00 = 0x00
    p_b_address_35f48fe_00 = 0x00
[FAIL] check_address_properties(address,address) (paths: 4, time: 0.12s, bounds: [])
Counterexample: 
    p_arr[0]_uint256_6ee061f_00 = 0x8000000000000000000000000000000000000000000000000000000000000000
    p_arr[1]_uint256_22023b2_00 = 0x00
    p_arr_length_3493a6d_00 = 0x02
[FAIL] check_array_sorted(uint256[]) (paths: 4, time: 0.12s, bounds: [arr=[0, 1, 2]])
Counterexample: 
    p_arr[0]_uint256_2dbaaf4_00 = 0x00
    p_arr[1]_uint256_d817d22_00 = 0x00
    p_arr_length_7ac293b_00 = 0x02
Counterexample: 
    p_arr_length_7ac293b_00 = 0x01
[FAIL] check_array_sum_property(uint256[]) (paths: 6, time: 0.13s, bounds: [arr=[0, 1, 2]])
Counterexample: 
    p_arr[0]_uint256_b8eb15d_00 = 0x00
    p_arr[1]_uint256_33ddbca_00 = 0x00
    p_arr_length_e45a0c2_00 = 0x02
[FAIL] check_array_unique_elements(uint256[]) (paths: 4, time: 0.11s, bounds: [arr=[0, 1, 2]])
Counterexample: 
    p_a_bool_7805565_00 = 0x01
    p_b_bool_ebbfd74_00 = 0x01
[FAIL] check_bool_implication(bool,bool) (paths: 5, time: 0.11s, bounds: [])
Counterexample: 
    p_a_bool_bc0ca0d_00 = 0x01
    p_b_bool_4874054_00 = 0x01
[FAIL] check_bool_xor_always_true(bool,bool) (paths: 4, time: 0.11s, bounds: [])
Counterexample: 
    p_x_uint256_c562caf_00 = 0x00
Counterexample: 
    p_x_uint256_c562caf_00 = 0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
[FAIL] check_boundary_conditions(uint256) (paths: 3, time: 0.12s, bounds: [])`;

console.log("🧪 Testing Halmos log parsing...\n");

// Test processLogs
console.log("=== processLogs result ===");
const result = processLogs(testLogs, Fuzzer.HALMOS);
console.log("Failed:", result.failed);
console.log("Passed:", result.passed);
console.log("Duration:", result.duration);
console.log("Number of broken properties:", result.brokenProperties.length);
console.log("Broken properties:");
result.brokenProperties.forEach((prop, i) => {
  console.log(`  ${i + 1}. ${prop.brokenProperty}`);
  console.log(`     Sequence: ${prop.sequence.substring(0, 100)}...`);
});

console.log("\n=== halmosLogsToFunctions result ===");
const foundryCode = halmosLogsToFunctions(testLogs, "test_run");
console.log(foundryCode);
